{"name": "furu-execution-strat", "version": "1.0.0", "description": "Automated DeFi Arbitrage Bot using Furucombo's Protocolink execution layer", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "validate": "ts-node src/scripts/quickValidation.ts", "validate:live": "ts-node src/scripts/validateLiveSystem.ts", "deploy:live": "ts-node src/scripts/deployLiveArbitrage.ts", "start:trading": "ts-node src/scripts/realArbitrageTrading.ts", "start:scaled": "ts-node src/scripts/scaledRealTrading.ts", "start:flashloan": "ts-node src/scripts/flashLoanTradingSystem.ts", "start:live": "ts-node src/scripts/liveFlashLoanDeployment.ts", "start:maximum": "ts-node src/scripts/maximumProfitTrading.ts", "start:demo": "ts-node src/scripts/quickDeploy.ts", "test:flash-loan": "ts-node src/scripts/testCompleteFlashLoanArbitrage.ts", "validate-production": "ts-node src/scripts/productionValidator.ts", "test-protocolink": "ts-node -e \"import('./src/core/protocolinkValidator').then(m => m.protocolinkValidator.validateProtocolinkExecution())\"", "generate-report": "ts-node -e \"import('./src/monitoring/plDashboard').then(m => m.plDashboard.generateReport().then(console.log))\"", "test:optimizations": "ts-node src/scripts/testOptimizations.ts", "check:wallet": "ts-node src/scripts/checkWalletInfo.ts", "monitor:optimizations": "ts-node -e \"import('./src/core/optimizationMonitor').then(m => { m.optimizationMonitor.startMonitoring(); console.log('Optimization monitoring started...'); })\"", "bootstrap:status": "ts-node src/scripts/bootstrapManager.ts", "bootstrap:enable": "ts-node src/scripts/enableBootstrap.ts", "bootstrap:disable": "ts-node src/scripts/disableBootstrap.ts", "safety:status": "ts-node src/scripts/safetyStopManager.ts", "safety:enable": "ts-node -e \"import('./src/core/safetyStop').then(m => { m.safetyStop.enable(); console.log('Safety stop enabled'); })\"", "safety:disable": "ts-node -e \"import('./src/core/safetyStop').then(m => { m.safetyStop.disable(); console.log('Safety stop disabled'); })\"", "safety:reset": "ts-node -e \"import('./src/core/safetyStop').then(m => { m.safetyStop.reset(); console.log('Safety stop reset'); })\"", "profit:analyze": "ts-node src/scripts/profitAnalysis.ts", "profit:discrepancy": "ts-node src/scripts/profitDiscrepancyAnalysis.ts", "capital:analyze": "ts-node src/scripts/lowCapitalStrategy.ts", "fees:analyze": "ts-node src/scripts/comprehensiveFeeAnalysis.ts", "demo:flashloan": "ts-node src/scripts/flashLoanDemo.ts", "genius:flashloan": "ts-node src/scripts/geniusFlashLoanSystem.ts", "hunt:opportunities": "ts-node src/scripts/aggressiveOpportunityHunter.ts", "corrected:flashloan": "ts-node src/scripts/correctedFlashLoanSystem.ts", "working:demo": "ts-node src/scripts/workingFlashLoanDemo.ts", "atomic:flashloan": "ts-node src/scripts/atomicFlashLoanExecution.ts", "live:trading": "ts-node src/scripts/liveTradingExecution.ts", "deploy:flashloan": "ts-node src/scripts/deployFlashLoanContract.ts", "execute:arbitrage": "ts-node src/scripts/executeFlashLoanArbitrage.ts", "scan:opportunities": "ts-node src/scripts/dynamicArbitrageScanner.ts", "deploy:real": "npx hardhat run scripts/deploy.ts --network mainnet", "compile": "npx hardhat compile", "deploy:direct": "ts-node src/scripts/directContractDeploy.ts", "print:money": "ts-node src/scripts/moneyPrintingMachine.ts", "test:contract": "ts-node src/scripts/testDeployedContract.ts", "deploy:working": "npx hardhat run scripts/deployWorkingContract.ts --network mainnet", "debug:flashloan": "ts-node src/scripts/debugFlashLoan.ts", "liquidate:profits": "ts-node src/scripts/liquidationMoneyPrinter.ts", "make:real:profits": "ts-node src/scripts/realProfitMaker.ts", "bulletproof:profits": "ts-node src/scripts/bulletproofProfitMaker.ts", "farm:yields": "ts-node src/scripts/yieldFarmingMoneyPrinter.ts", "massive:scale": "ts-node src/scripts/massiveScaleMoneyPrinter.ts", "diagnose:contract": "ts-node src/scripts/contractDiagnostic.ts", "test:progressive": "ts-node src/scripts/progressiveScaleTester.ts", "deploy:production": "ts-node src/scripts/deployProductionContract.ts", "test:micro": "ts-node src/scripts/microScaleTester.ts", "repair:contract": "ts-node src/scripts/contractRepairEngine.ts", "execute:live": "ts-node src/scripts/liveProfitExecutor.ts", "execute:protocolink": "ts-node src/scripts/protocolinkNativeExecutor.ts", "arbitrage:real": "ts-node src/scripts/realProtocolinkArbitrage.ts", "execute:real": "ts-node src/scripts/realFlashLoanExecutor.ts", "arbitrage:actual": "ts-node src/scripts/actualFlashLoanArbitrage.ts", "execute:sdk": "ts-node src/scripts/protocolinkSDKExecutor.ts", "execute:final": "ts-node src/scripts/realProtocolinkExecution.ts", "arbitrage:minimal": "ts-node src/scripts/minimalFlashLoanArbitrage.ts", "monitor:continuous": "ts-node src/scripts/continuousArbitrageMonitor.ts", "verify:system": "ts-node src/scripts/contractVerificationAnalysis.ts", "execute:optimized": "ts-node src/scripts/optimizedArbitrageExecutor.ts", "execute:ultra": "ts-node src/scripts/ultraEfficientArbitrage.ts", "analyze:profits": "ts-node src/scripts/profitableTransactionAnalyzer.ts", "scan:liquidations": "ts-node src/scripts/liquidationScanner.ts", "analyze:real": "ts-node src/scripts/realTransactionAnalyzer.ts", "scan:protocolink": "ts-node src/scripts/protocolinkLiquidationScanner.ts", "execute:liquidation": "ts-node src/scripts/liquidationExecutor.ts", "scan:simple": "ts-node src/scripts/simpleLiquidationScanner.ts", "proof:liquidation": "ts-node src/scripts/realLiquidationProof.ts", "analyze:guaranteed": "ts-node src/scripts/guaranteedProfitAnalyzer.ts", "monitor:prices": "ts-node src/scripts/realTimePriceMonitor.ts", "plan:progression": "ts-node src/scripts/capitalProgressionPlan.ts", "execute:stage1": "ts-node src/scripts/stage1Executor.ts", "plan:realistic": "ts-node src/scripts/realisticCapitalGrowth.ts", "execute:realistic": "ts-node src/scripts/stage1RealisticExecution.ts", "zero:capital": "ts-node src/scripts/zeroCapitalYieldFarming.ts", "deploy:zero-capital": "npx hardhat run scripts/deployZeroCapitalYieldFarmer.ts --network ethereum", "test:zero-capital": "ts-node src/scripts/testZeroCapitalSystem.ts", "research:flashloan": "ts-node src/scripts/flashLoanResearch.ts", "monitor:arbitrage": "ts-node src/scripts/realTimeArbitrageMonitor.ts", "analyze:strategies": "ts-node src/scripts/flashLoanStrategyAnalysis.ts", "implement:top3": "ts-node src/scripts/top3StrategyImplementation.ts", "alpha:scan": "ts-node alpha-scanner/scanner/dust_funnel_drain.js", "alpha:execute": "ts-node alpha-scanner/executor/cli.js", "alpha:simulate": "ts-node alpha-scanner/simulator/tenderly.js", "alpha:deploy": "ts-node alpha-scanner/executor/deploy.js", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["defi", "arbitrage", "furucombo", "protocolink", "flash-loans"], "author": "", "license": "MIT", "dependencies": {"@aave/core-v3": "^1.19.3", "@flashbots/ethers-provider-bundle": "^1.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "@openzeppelin/contracts": "^4.9.6", "@protocolink/api": "^1.4.8", "@protocolink/common": "^0.5.5", "@protocolink/core": "^0.6.4", "@protocolink/logics": "^1.8.9", "@uniswap/v2-periphery": "^1.1.0-beta.0", "@uniswap/v3-periphery": "^1.4.4", "axios": "^1.6.0", "big.js": "^6.2.1", "commander": "^11.1.0", "dotenv": "^16.5.0", "ethers": "^6.14.3", "hardhat-contract-sizer": "^2.10.0", "hardhat-gas-reporter": "^2.3.0", "lodash": "^4.17.21", "node-cron": "^3.0.3", "uuid": "^9.0.0", "winston": "^3.11.0", "ws": "^8.14.0"}, "devDependencies": {"@nomicfoundation/hardhat-ignition": "^0.15.11", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/ignition-core": "^0.15.11", "@types/big.js": "^6.2.0", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.0", "@types/node": "^20.8.0", "@types/uuid": "^9.0.8", "@types/ws": "^8.5.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "hardhat": "^2.24.2", "jest": "^29.7.0", "prettier": "^3.0.0", "ts-node": "^10.9.0", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0"}}